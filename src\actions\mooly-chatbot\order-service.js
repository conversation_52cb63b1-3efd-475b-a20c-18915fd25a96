import { useRef, useMemo, useState, useEffect, useCallback } from 'react';

import { getAddressById } from './customer-service';
import { fetchData, createData, updateData, deleteData, upsertData, callRPC } from './supabase-utils';
import {
  TABLE_NAME,
  ORDER_ITEMS_TABLE,
  ORDER_HISTORY_TABLE,
  ORDER_STATUSES_TABLE,
  DEFAULT_ORDER_OPTIONS,
} from './order-constants';

/**
 * Parse variant info từ string JSON hoặc object
 * @param {string|Object} variantInfo - Thông tin variant
 * @returns {Object} - Thông tin variant đã parse
 */
function parseVariantInfo(variantInfo) {
  if (!variantInfo) return {};

  if (typeof variantInfo === 'string') {
    try {
      return JSON.parse(variantInfo);
    } catch (error) {
      console.warn('Failed to parse variant info:', variantInfo, error);
      return {};
    }
  }

  if (typeof variantInfo === 'object') {
    return variantInfo;
  }

  return {};
}



/**
 * <PERSON><PERSON>y danh sách đơn hàng với các tùy chọn lọc
 * @param {Object} options - Các tùy chọn truy vấn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getOrders(options = {}) {
  return fetchData(TABLE_NAME, options);
}

/**
 * Lấy thông tin chi tiết đơn hàng theo ID với đầy đủ thông tin liên quan
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getOrderById(orderId) {
  if (!orderId) {
    return { success: false, error: new Error('Thiếu ID đơn hàng'), data: null };
  }

  try {
    // Lấy thông tin đơn hàng với JOIN để có đầy đủ thông tin
    const orderResult = await fetchData(TABLE_NAME, {
      filters: { id: orderId },
      single: true,
      columns: `
        *,
        customers:customer_id (
          id,
          full_name,
          email,
          phone,
          avatar_url,
          notes
        ),
        shipping_addresses:shipping_address_id (
          id,
          full_name,
          phone,
          address,
          address_line2,
          city,
          state,
          postal_code,
          country,
          province,
          district,
          ward,
          notes,
          address_type,
          is_default,
          is_default_shipping,
          is_default_billing
        ),
        billing_addresses:billing_address_id (
          id,
          full_name,
          phone,
          address,
          address_line2,
          city,
          state,
          postal_code,
          country,
          province,
          district,
          ward,
          notes,
          address_type,
          is_default,
          is_default_shipping,
          is_default_billing
        )
      `,
    });

    if (!orderResult.success || !orderResult.data) {
      return orderResult;
    }

    // Chuẩn hóa dữ liệu để đảm bảo tương thích với form
    const order = orderResult.data;
    const normalizedOrder = {
      ...order,
      // Đảm bảo các field được map đúng cho form
      customerInfo: order.customers ? {
        id: order.customers.id,
        fullName: order.customers.fullName || order.customers.full_name,
        email: order.customers.email,
        phone: order.customers.phone,
        avatar: order.customers.avatarUrl || order.customers.avatar_url,
        notes: order.customers.notes
      } : null,
      shippingAddressInfo: order.shippingAddresses ? {
        id: order.shippingAddresses.id,
        fullName: order.shippingAddresses.fullName || order.shippingAddresses.full_name,
        phone: order.shippingAddresses.phone,
        address: order.shippingAddresses.address,
        addressLine2: order.shippingAddresses.addressLine2 || order.shippingAddresses.address_line2,
        city: order.shippingAddresses.city,
        state: order.shippingAddresses.state,
        postalCode: order.shippingAddresses.postalCode || order.shippingAddresses.postal_code,
        country: order.shippingAddresses.country,
        province: order.shippingAddresses.province,
        district: order.shippingAddresses.district,
        ward: order.shippingAddresses.ward,
        notes: order.shippingAddresses.notes,
        addressType: order.shippingAddresses.addressType || order.shippingAddresses.address_type,
        isDefault: order.shippingAddresses.isDefault || order.shippingAddresses.is_default,
        isDefaultShipping: order.shippingAddresses.isDefaultShipping || order.shippingAddresses.is_default_shipping,
        isDefaultBilling: order.shippingAddresses.isDefaultBilling || order.shippingAddresses.is_default_billing
      } : null,
      billingAddressInfo: order.billingAddresses ? {
        id: order.billingAddresses.id,
        fullName: order.billingAddresses.fullName || order.billingAddresses.full_name,
        phone: order.billingAddresses.phone,
        address: order.billingAddresses.address,
        addressLine2: order.billingAddresses.addressLine2 || order.billingAddresses.address_line2,
        city: order.billingAddresses.city,
        state: order.billingAddresses.state,
        postalCode: order.billingAddresses.postalCode || order.billingAddresses.postal_code,
        country: order.billingAddresses.country,
        province: order.billingAddresses.province,
        district: order.billingAddresses.district,
        ward: order.billingAddresses.ward,
        notes: order.billingAddresses.notes,
        addressType: order.billingAddresses.addressType || order.billingAddresses.address_type,
        isDefault: order.billingAddresses.isDefault || order.billingAddresses.is_default,
        isDefaultShipping: order.billingAddresses.isDefaultShipping || order.billingAddresses.is_default_shipping,
        isDefaultBilling: order.billingAddresses.isDefaultBilling || order.billingAddresses.is_default_billing
      } : null
    };

    return {
      success: true,
      data: normalizedOrder,
      error: null
    };
  } catch (error) {
    console.error('Error in getOrderById:', error);
    return { success: false, error, data: null };
  }
}

/**
 * Lấy danh sách sản phẩm trong đơn hàng với thông tin đầy đủ
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getOrderItems(orderId) {
  if (!orderId) {
    return { success: false, error: new Error('Thiếu ID đơn hàng'), data: [] };
  }

  try {
    const itemsResult = await fetchData(ORDER_ITEMS_TABLE, {
      filters: { orderId },
      columns: `
        *,
        products:product_id (
          id,
          name,
          description,
          selling_price,
          images,
          avatar,
          type,
          sku,
          barcode,
          stock_quantity,
          attributes
        ),
        variants:variant_id (
          id,
          name,
          sku,
          barcode,
          selling_price,
          attributes,
          avatar,
          stock_quantity
        )
      `,
    });

    if (!itemsResult.success) {
      return itemsResult;
    }

    // Chuẩn hóa dữ liệu order items để đảm bảo tương thích với form
    const normalizedItems = (itemsResult.data || []).map((item) => {
      const productDetail = item.products ? {
        id: item.products.id,
        name: item.products.name,
        description: item.products.description,
        sellingPrice: Number(item.products.sellingPrice || item.products.selling_price || 0),
        images: item.products.images || [],
        avatar: item.products.avatar,
        type: item.products.type,
        sku: item.products.sku,
        barcode: item.products.barcode,
        stockQuantity: item.products.stockQuantity || item.products.stock_quantity,
        attributes: item.products.attributes
      } : null;

      const variantDetail = item.variants ? {
        id: item.variants.id,
        name: item.variants.name,
        sku: item.variants.sku,
        barcode: item.variants.barcode,
        sellingPrice: Number(item.variants.sellingPrice || item.variants.selling_price || 0),
        attributes: item.variants.attributes,
        avatar: item.variants.avatar,
        stockQuantity: item.variants.stockQuantity || item.variants.stock_quantity
      } : null;

      // Xử lý thông tin variantInfo nếu có
      const variantAttributes = parseVariantInfo(item.variantInfo || item.variant_info);

      return {
        ...item,
        productDetail,
        variantDetail,
        variantAttributes,
        // Ưu tiên lấy hình ảnh từ variant, nếu không có thì lấy từ product
        imageUrl: item.imageUrl ||
                  variantDetail?.avatar ||
                  productDetail?.avatar ||
                  productDetail?.images?.[0] ||
                  null
      };
    });

    return {
      success: true,
      data: normalizedItems,
      error: null
    };
  } catch (error) {
    console.error('Error in getOrderItems:', error);
    return { success: false, error, data: [] };
  }
}

/**
 * Lấy lịch sử trạng thái đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getOrderHistory(orderId) {
  if (!orderId) {
    return { success: false, error: new Error('Thiếu ID đơn hàng'), data: [] };
  }

  return fetchData(ORDER_HISTORY_TABLE, {
    filters: { order_id: orderId },
    orderBy: 'createdAt',
    ascending: false,
  });
}

/**
 * Lấy danh sách trạng thái đơn hàng
 * @param {string} storeId - ID cửa hàng
 * @param {string} productType - Loại sản phẩm (simple, variable, digital, service, all)
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getOrderStatuses(storeId, productType = 'all') {
  if (!storeId) {
    return { success: false, error: new Error('Thiếu ID cửa hàng'), data: [] };
  }

  // Lấy cả trạng thái chung (all) và trạng thái theo loại sản phẩm
  return fetchData(ORDER_STATUSES_TABLE, {
    filters: {
      store_id: storeId,
      product_type: [productType, 'all'],
    },
    orderBy: 'sort_order',
    ascending: true,
  });
}

/**
 * Tạo đơn hàng mới
 * @param {Object} orderData - Dữ liệu đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createOrder(orderData) {
  return createData(TABLE_NAME, orderData);
}

/**
 * Tạo sản phẩm trong đơn hàng
 * @param {Object} orderItemData - Dữ liệu sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createOrderItem(orderItemData) {
  return createData(ORDER_ITEMS_TABLE, orderItemData);
}

/**
 * Tạo lịch sử trạng thái đơn hàng
 * @param {Object} historyData - Dữ liệu lịch sử
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createOrderHistory(historyData) {
  try {
    // Sử dụng RPC function để tạo order history với tenant_id được xử lý đúng cách
    const result = await callRPC('create_order_history_entry', {
      p_order_id: historyData.orderId,
      p_status: historyData.status,
      p_comment: historyData.comment || '',
      p_user_id: historyData.userId || null,
      p_previous_status: historyData.previousStatus || null
    });

    if (result.success && result.data) {
      return {
        success: true,
        data: [{ id: result.data }], // Giữ format tương thích với createData
        error: null
      };
    } else {
      return {
        success: false,
        error: result.error || 'Không thể tạo lịch sử đơn hàng',
        data: null
      };
    }
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo lịch sử đơn hàng',
      data: null
    };
  }
}

/**
 * Cập nhật đơn hàng
 * @param {string} orderId - ID đơn hàng
 * @param {Object} orderData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateOrder(orderId, orderData) {
  if (!orderId) {
    return { success: false, error: new Error('Thiếu ID đơn hàng'), data: null };
  }

  return updateData(TABLE_NAME, orderData, { id: orderId });
}

/**
 * Cập nhật sản phẩm trong đơn hàng
 * @param {string} orderItemId - ID sản phẩm
 * @param {Object} orderItemData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateOrderItem(orderItemId, orderItemData) {
  if (!orderItemId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  return updateData(ORDER_ITEMS_TABLE, orderItemData, { id: orderItemId });
}

/**
 * Hủy đơn hàng (thay thế cho xóa đơn hàng)
 * @param {string} orderId - ID đơn hàng
 * @param {string} reason - Lý do hủy đơn hàng
 * @param {Object} options - Tùy chọn
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function cancelOrder(orderId, reason = 'Hủy đơn hàng', options = {}) {
  if (!orderId) {
    return { success: false, error: new Error('Thiếu ID đơn hàng'), data: null };
  }

  try {
    // Import updateOrderStatusEnhanced để sử dụng
    const { updateOrderStatusEnhanced } = await import('./enhanced-order-service');

    // Cập nhật trạng thái đơn hàng thành 'cancelled'
    const result = await updateOrderStatusEnhanced(orderId, 'cancelled', {
      comment: reason,
      autoInventoryUpdate: true, // Tự động hoàn lại inventory
      notifyCustomer: true,
      ...options
    });

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi hủy đơn hàng',
      data: null
    };
  }
}

/**
 * @deprecated Sử dụng cancelOrder thay thế
 * Xóa đơn hàng (deprecated - chỉ dùng cho trường hợp đặc biệt)
 * @param {string} orderId - ID đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteOrder(orderId) {
  console.warn('deleteOrder is deprecated. Use cancelOrder instead.');

  if (!orderId) {
    return { success: false, error: new Error('Thiếu ID đơn hàng'), data: null };
  }

  // Xóa tất cả sản phẩm trong đơn hàng trước
  await deleteData(ORDER_ITEMS_TABLE, { order_id: orderId });

  // Xóa lịch sử trạng thái đơn hàng
  await deleteData(ORDER_HISTORY_TABLE, { order_id: orderId });

  // Xóa đơn hàng
  return deleteData(TABLE_NAME, { id: orderId });
}

/**
 * Xóa sản phẩm trong đơn hàng
 * @param {string} orderItemId - ID sản phẩm
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteOrderItem(orderItemId) {
  if (!orderItemId) {
    return { success: false, error: new Error('Thiếu ID sản phẩm'), data: null };
  }

  return deleteData(ORDER_ITEMS_TABLE, { id: orderItemId });
}

/**
 * Tạo hoặc cập nhật đơn hàng
 * @param {Object} orderData - Dữ liệu đơn hàng
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function upsertOrder(orderData) {
  return upsertData(TABLE_NAME, orderData);
}

/**
 * Hook để lấy danh sách đơn hàng với tối ưu performance
 * @param {Object} options - Các tùy chọn
 * @returns {Object} - Kết quả từ API
 */
export function useOrders(options = {}) {
  const [orders, setOrders] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isValidating, setIsValidating] = useState(false);
  const [totalCount, setTotalCount] = useState(0);

  // Tạo stable key từ options để tránh infinite re-renders
  const optionsKey = useMemo(() => {
    const {
      filters = {},
      orderBy,
      ascending,
      limit,
      offset,
      count
    } = options;

    // Tạo key ổn định từ các giá trị quan trọng
    return JSON.stringify({
      status: filters.status,
      or: filters.or,
      createdAt: filters.createdAt,
      orderBy,
      ascending,
      limit,
      offset,
      count
    });
  }, [options]);

  // Memoize options với key ổn định - KHÔNG phụ thuộc vào optionsKey
  const memoizedOptions = useMemo(() => ({
    ...DEFAULT_ORDER_OPTIONS,
    ...options
  }), [JSON.stringify(options)]);

  // Stable reference cho fetchOrders - KHÔNG phụ thuộc vào state
  const fetchOrders = useCallback(async (optionsToUse) => {
    setIsValidating(true);
    setError(null);

    try {
      // Ensure we get count for pagination
      const finalOptions = { ...optionsToUse };
      const countEnabled = finalOptions.count !== false;
      if (countEnabled) {
        finalOptions.count = true;
      }

      console.log('🔄 Fetching orders with options:', finalOptions);
      const result = await getOrders(finalOptions);

      if (result.success) {
        setOrders(result.data || []);
        if (countEnabled) {
          setTotalCount(result.count || 0);
        }
        setError(null);
      } else {
        setError(result.error);
        console.error('❌ Error fetching orders:', result.error);
      }

      return result;
    } catch (err) {
      const errorMessage = err?.message || 'Lỗi không xác định khi tải đơn hàng';
      setError(errorMessage);
      console.error('💥 Exception in fetchOrders:', err);
      return { success: false, error: errorMessage, data: null };
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, []); // Không phụ thuộc vào bất kỳ state nào

  // Sử dụng một useEffect duy nhất để tránh conflict
  const prevOptionsKeyRef = useRef(null);
  const isFirstLoadRef = useRef(true);

  useEffect(() => {
    let isMounted = true;
    let timeoutId;

    const loadData = async () => {
      if (isMounted) {
        setIsLoading(true);
        await fetchOrders(memoizedOptions);
      }
    };

    // Kiểm tra xem có cần load data không
    const shouldLoad = prevOptionsKeyRef.current !== optionsKey;

    if (shouldLoad) {
      prevOptionsKeyRef.current = optionsKey;

      if (isFirstLoadRef.current) {
        // Lần đầu - load ngay lập tức
        isFirstLoadRef.current = false;
        loadData();
      } else {
        // Options thay đổi - debounce
        timeoutId = setTimeout(loadData, 300);
      }
    }

    return () => {
      isMounted = false;
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [optionsKey, fetchOrders, memoizedOptions]); // Chỉ phụ thuộc vào optionsKey

  // Function to manually refresh data - memoized để tránh re-render
  const mutate = useCallback(async () => {
    console.log('🔄 Manually refreshing orders data...');
    setIsLoading(true);
    return fetchOrders(memoizedOptions);
  }, [fetchOrders, memoizedOptions]);

  const memoizedValue = useMemo(
    () => ({
      orders,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      totalCount,
      mutate,
      isEmpty: !isLoading && !isValidating && (!orders || orders.length === 0),
    }),
    [orders, error, isLoading, isValidating, totalCount, mutate]
  );

  return memoizedValue;
}

/**
 * Hook để lấy thông tin chi tiết đơn hàng
 * @param {string} orderId - ID đơn hàng cần lấy
 * @returns {Object} - Kết quả từ API
 */
export function useOrder(orderId) {
  const [order, setOrder] = useState(null);
  const [orderItems, setOrderItems] = useState([]);
  const [orderHistory, setOrderHistory] = useState([]);
  const [shippingAddress, setShippingAddress] = useState(null);
  const [billingAddress, setBillingAddress] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isValidating, setIsValidating] = useState(false);

  const fetchOrder = useCallback(async () => {
    if (!orderId) {
      setOrder(null);
      setOrderItems([]);
      setOrderHistory([]);
      setShippingAddress(null);
      setBillingAddress(null);
      return;
    }

    setIsValidating(true);
    setError(null);
    try {
      // Sử dụng Promise.all để thực hiện các truy vấn song song
      const [orderResult, itemsResult, historyResult] = await Promise.all([
        // Lấy thông tin đơn hàng
        getOrderById(orderId),
        // Lấy thông tin sản phẩm trong đơn hàng kèm chi tiết product và variant
        getOrderItems(orderId),
        // Lấy lịch sử trạng thái đơn hàng
        getOrderHistory(orderId),
      ]);

      if (orderResult.success && orderResult.data) {
        setOrder(orderResult.data);

        // Lấy địa chỉ từ dữ liệu đã join hoặc từ API riêng biệt
        if (orderResult.data.shippingAddresses) {
          setShippingAddress(orderResult.data.shippingAddresses);
        } else if (orderResult.data.shippingAddressId) {
          try {
            const shippingAddressResult = await getAddressById(orderResult.data.shippingAddressId);
            console.log('Shipping address result:', shippingAddressResult);
            if (shippingAddressResult.success && shippingAddressResult.data) {
              setShippingAddress(shippingAddressResult.data);
            }
          } catch (err) {
            console.error('Lỗi khi lấy địa chỉ giao hàng:', err);
          }
        }

        if (orderResult.data.billingAddresses) {
          setBillingAddress(orderResult.data.billingAddresses);
        } else if (orderResult.data.billingAddressId) {
          try {
            // Nếu địa chỉ thanh toán giống địa chỉ giao hàng
            if (
              orderResult.data.billingAddressId === orderResult.data.shippingAddressId &&
              (orderResult.data.shippingAddresses || shippingAddress)
            ) {
              setBillingAddress(orderResult.data.shippingAddresses || shippingAddress);
            } else {
              const billingAddressResult = await getAddressById(orderResult.data.billingAddressId);
              console.log('Billing address result:', billingAddressResult);
              if (billingAddressResult.success && billingAddressResult.data) {
                setBillingAddress(billingAddressResult.data);
              }
            }
          } catch (err) {
            console.error('Lỗi khi lấy địa chỉ thanh toán:', err);
          }
        }

        // Xử lý dữ liệu orderItems - dữ liệu đã được chuẩn hóa từ getOrderItems
        if (itemsResult.success && itemsResult.data) {
          setOrderItems(itemsResult.data);
        } else {
          setOrderItems([]);
        }

        setOrderHistory(historyResult.success ? historyResult.data : []);
      } else {
        setError(orderResult.error || new Error('Không tìm thấy đơn hàng'));
      }
    } catch (err) {
      setError(err);
    } finally {
      setIsValidating(false);
      setIsLoading(false);
    }
  }, [orderId]);

  // Initial data loading
  useEffect(() => {
    setIsLoading(true);
    fetchOrder();
  }, [fetchOrder]);

  // Function to manually refresh data
  const mutate = useCallback(async () => {
    console.log('Manually refreshing order data...');
    return fetchOrder();
  }, [fetchOrder]);

  const memoizedValue = useMemo(
    () => ({
      order,
      orderItems,
      orderHistory,
      shippingAddress,
      billingAddress,
      isLoading,
      isError: !!error,
      error,
      isValidating,
      mutate,
      isEmpty: !isLoading && !isValidating && !order,
    }),
    [
      order,
      orderItems,
      orderHistory,
      shippingAddress,
      billingAddress,
      error,
      isLoading,
      isValidating,
      mutate,
    ]
  );

  return memoizedValue;
}

/**
 * Hook để lấy danh sách trạng thái đơn hàng
 * @param {string} storeId - ID cửa hàng
 * @param {string} productType - Loại sản phẩm (simple, variable, digital, service, all)
 * @returns {Object} - Kết quả từ API
 */
export function useOrderStatuses(storeId, productType = 'all') {
  const [statuses, setStatuses] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);

  const fetchStatuses = useCallback(async () => {
    if (!storeId) return;

    setIsLoading(true);
    try {
      const result = await getOrderStatuses(storeId, productType);
      if (result.success) {
        setStatuses(result.data || []);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err);
    } finally {
      setIsLoading(false);
    }
  }, [storeId, productType]);

  useEffect(() => {
    fetchStatuses();
  }, [fetchStatuses]);

  // Function to manually refresh data
  const mutate = useCallback(() => fetchStatuses(), [fetchStatuses]);

  return {
    statuses,
    isLoading,
    isError: !!error,
    error,
    mutate,
  };
}

/**
 * Hook để tạo, cập nhật, xóa đơn hàng
 * @returns {Object} - Các hàm mutation
 */
export function useOrderMutations() {
  const [loadingStates, setLoadingStates] = useState({
    creating: false,
    updating: false,
    deleting: false,
    cancelling: false,
    upserting: false,
    creatingItem: false,
    updatingItem: false,
    deletingItem: false,
    creatingHistory: false,
  });

  // Helper function to handle loading state and error handling
  const withLoadingState = async (stateKey, asyncFn) => {
    setLoadingStates((prev) => ({ ...prev, [stateKey]: true }));
    try {
      return await asyncFn();
    } finally {
      setLoadingStates((prev) => ({ ...prev, [stateKey]: false }));
    }
  };

  // Mutation functions using the helper
  const createOrderMutation = (orderData) =>
    withLoadingState('creating', () => createOrder(orderData));

  const updateOrderMutation = (id, data) =>
    withLoadingState('updating', () => updateOrder(id, data));

  const deleteOrderMutation = (orderId) => withLoadingState('deleting', () => deleteOrder(orderId));
  const cancelOrderMutation = (orderId, reason, options) => withLoadingState('cancelling', () => cancelOrder(orderId, reason, options));

  const upsertOrderMutation = (orderData) =>
    withLoadingState('upserting', () => upsertOrder(orderData));

  const createOrderItemMutation = (itemData) =>
    withLoadingState('creatingItem', () => createOrderItem(itemData));

  const updateOrderItemMutation = (itemId, itemData) =>
    withLoadingState('updatingItem', () => updateOrderItem(itemId, itemData));

  const deleteOrderItemMutation = (itemId) =>
    withLoadingState('deletingItem', () => deleteOrderItem(itemId));

  const createOrderHistoryMutation = (historyData) =>
    withLoadingState('creatingHistory', () => createOrderHistory(historyData));

  // Calculate if any mutation is in progress
  const isMutating = Object.values(loadingStates).some(Boolean);

  return {
    createOrder: createOrderMutation,
    updateOrder: updateOrderMutation,
    deleteOrder: deleteOrderMutation,
    cancelOrder: cancelOrderMutation,
    upsertOrder: upsertOrderMutation,
    createOrderItem: createOrderItemMutation,
    updateOrderItem: updateOrderItemMutation,
    deleteOrderItem: deleteOrderItemMutation,
    createOrderHistory: createOrderHistoryMutation,
    isCreating: loadingStates.creating,
    isUpdating: loadingStates.updating,
    isDeleting: loadingStates.deleting,
    isCancelling: loadingStates.cancelling,
    isUpserting: loadingStates.upserting,
    isCreatingItem: loadingStates.creatingItem,
    isUpdatingItem: loadingStates.updatingItem,
    isDeletingItem: loadingStates.deletingItem,
    isCreatingHistory: loadingStates.creatingHistory,
    isMutating,
  };
}

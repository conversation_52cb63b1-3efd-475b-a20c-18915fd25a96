 
import PropTypes from 'prop-types';

import Box from '@mui/material/Box';
import Grid from '@mui/material/Grid';
import Card from '@mui/material/Card';
import Stack from '@mui/material/Stack';
import Button from '@mui/material/Button';
import Dialog from '@mui/material/Dialog';
import Typography from '@mui/material/Typography';
import DialogTitle from '@mui/material/DialogTitle';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';

import { fileData, FileThumbnail } from 'src/components/file-thumbnail';

/**
 * Component dialog chọn hình ảnh cho biến thể
 */
export default function ImageSelector({ open, media, onClose, onSelectImage, selectedImage }) {
  // Hàm kiểm tra xem hình ảnh đã được chọn chưa
  const isSelected = (image) => {
    if (!selectedImage) return false;

    try {
      // Xử lý trường hợp selectedImage là URL string
      if (typeof selectedImage === 'string') {
        const imageUrl = typeof image === 'string' ? image : URL.createObjectURL(image);
        return selectedImage === imageUrl;
      }

      // Xử lý trường hợp selectedImage là object có url
      if (selectedImage.url) {
        const imageUrl =
          typeof image === 'string' ? image : image.url || URL.createObjectURL(image);
        return selectedImage.url === imageUrl;
      }

      // Xử lý trường hợp selectedImage là File object
      if (selectedImage instanceof File || selectedImage.name) {
        // Nếu image cũng là File, so sánh theo name và size
        if (image instanceof File || image.name) {
          return selectedImage.name === image.name && selectedImage.size === image.size;
        }
        return false;
      }

      return false;
    } catch (error) {
      return false;
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Chọn ảnh cho biến thể</DialogTitle>
      <DialogContent>
        {Array.isArray(media) && media.length > 0 ? (
          <Grid container spacing={2} sx={{ mt: 1 }}>
            {media.map((image, index) => {
              if (!image) {
                return null;
              }

              // Xử lý an toàn với fileData
              let name = '';
              try {
                const fileInfo = fileData(image);
                name = fileInfo?.name || '';
              } catch (error) {
                return;
              }

              return (
                <Grid item size={{ xs: 6, sm: 4, md: 3 }} key={index}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      height: 140,
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      transition: 'all 0.2s',
                      position: 'relative',
                      border: (theme) =>
                        isSelected(image) ? `2px solid ${theme.palette.primary.main}` : 'none',
                      '&:hover': {
                        transform: 'scale(1.05)',
                        boxShadow: 6,
                      },
                    }}
                    onClick={() => {
                      try {
                        // Tối ưu: Đơn giản hóa - luôn truyền URL string
                        let imageUrl = image;
                        if (typeof image === 'object' && image !== null) {
                          if (image.url) {
                            imageUrl = image.url;
                          } else if (image instanceof File) {
                            imageUrl = image; // Giữ nguyên File object
                          }
                        }
                        onSelectImage(imageUrl);
                      } catch (error) {
                        return;
                      }
                    }}
                  >
                    <Stack
                      alignItems="center"
                      justifyContent="center"
                      sx={{ width: '100%', height: '100%', p: 1 }}
                    >
                      {(() => {
                        try {
                          // Chuẩn hóa dữ liệu hình ảnh trước khi truyền vào FileThumbnail
                          let safeImage = image;

                          // Nếu image là object có url, chuyển thành string URL
                          if (image && typeof image === 'object' && image.url) {
                            safeImage = image.url;
                          }

                          return (
                            <FileThumbnail
                              file={safeImage}
                              tooltip
                              imageView
                              sx={{ width: '100%', height: '100%', borderRadius: 1 }}
                              slotProps={{
                                img: {
                                  sx: {
                                    objectFit: 'contain',
                                  },
                                  onError: (e) => {
                                    // Thay thế bằng hình ảnh mặc định khi lỗi
                                    e.target.src = '/assets/placeholder.svg';
                                  },
                                },
                              }}
                            />
                          );
                        } catch (error) {
                          return (
                            <Box
                              sx={{
                                width: '100%',
                                height: '100%',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                bgcolor: 'background.neutral',
                                borderRadius: 1,
                              }}
                            >
                              <Typography variant="caption" color="text.disabled">
                                Lỗi hiển thị
                              </Typography>
                            </Box>
                          );
                        }
                      })()}

                      {name && (
                        <Typography
                          variant="caption"
                          noWrap
                          sx={{
                            mt: 0.5,
                            maxWidth: '100%',
                            textAlign: 'center',
                          }}
                        >
                          {name}
                        </Typography>
                      )}

                      {isSelected(image) && (
                        <Box
                          sx={{
                            position: 'absolute',
                            top: 8,
                            right: 8,
                            width: 20,
                            height: 20,
                            borderRadius: '50%',
                            bgcolor: 'primary.main',
                            display: 'flex',
                            alignItems: 'center',
                            justifyContent: 'center',
                            color: 'primary.contrastText',
                            fontSize: 14,
                            fontWeight: 'bold',
                          }}
                        >
                          ✓
                        </Box>
                      )}
                    </Stack>
                  </Card>
                </Grid>
              );
            })}
          </Grid>
        ) : (
          <Stack spacing={2} sx={{ my: 3, alignItems: 'center' }}>
            <Box sx={{ textAlign: 'center' }}>
              <img
                src="/assets/icons/empty/ic-content.svg"
                alt="No images"
                style={{ width: 120, height: 120, opacity: 0.7 }}
              />
            </Box>
            <Typography variant="body1" sx={{ textAlign: 'center' }}>
              Không có ảnh sản phẩm. Vui lòng tải lên ảnh trong mục &quot;Hình ảnh & Video&quot;.
            </Typography>
            <Button
              variant="outlined"
              color="primary"
              onClick={onClose}
              startIcon={
                <Box component="span" className="material-icons-round">
                  add_photo_alternate
                </Box>
              }
            >
              Quay lại để thêm ảnh
            </Button>
          </Stack>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Đóng</Button>
      </DialogActions>
    </Dialog>
  );
}

ImageSelector.propTypes = {
  open: PropTypes.bool.isRequired,
  media: PropTypes.array.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelectImage: PropTypes.func.isRequired,
  selectedImage: PropTypes.oneOfType([PropTypes.string, PropTypes.object]),
};
